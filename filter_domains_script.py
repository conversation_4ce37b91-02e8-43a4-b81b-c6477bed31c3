#!/usr/bin/env python3
"""
Script to filter domains from makeup and furniture CSV files that already have clicks in shopmy.csv

This script:
1. Extracts domains from shopmy.csv where clicks > 0
2. Filters out these domains from makeup and furniture CSV files
3. Returns domains that have 0 clicks in shopmy.csv
"""

import csv
import pandas as pd
from urllib.parse import urlparse
import re
import sys
from pathlib import Path

def extract_domain_from_url(url):
    """
    Extract and normalize domain from URL
    
    Args:
        url (str): URL to extract domain from
        
    Returns:
        str: Normalized domain name or None if invalid
    """
    if not url or pd.isna(url):
        return None
    
    try:
        # Handle URLs that might not have protocol
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix for normalization
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Remove port numbers if present
        if ':' in domain:
            domain = domain.split(':')[0]
        
        return domain if domain else None
        
    except Exception as e:
        print(f"Error parsing URL '{url}': {e}")
        return None

def get_domains_with_clicks(shopmy_file):
    """
    Extract domains from shopmy.csv that have clicks > 0
    
    Args:
        shopmy_file (str): Path to shopmy.csv file
        
    Returns:
        set: Set of domains that have clicks
    """
    domains_with_clicks = set()
    
    try:
        df = pd.read_csv(shopmy_file)
        print(f"Loaded {len(df)} rows from {shopmy_file}")
        
        # Filter for rows with clicks > 0
        # Handle both numeric and string representations
        df['Clicks'] = pd.to_numeric(df['Clicks'], errors='coerce')
        df_with_clicks = df[df['Clicks'] > 0]
        
        print(f"Found {len(df_with_clicks)} rows with clicks > 0")
        
        # Extract domains from URL column (last column)
        url_column = df_with_clicks.columns[-1]  # Last column should be URL
        print(f"Extracting domains from column: {url_column}")
        
        for idx, url in enumerate(df_with_clicks[url_column]):
            domain = extract_domain_from_url(url)
            if domain:
                domains_with_clicks.add(domain)
            
            # Progress indicator for large files
            if (idx + 1) % 500 == 0:
                print(f"Processed {idx + 1} URLs...")
        
        print(f"Extracted {len(domains_with_clicks)} unique domains with clicks")
        
    except Exception as e:
        print(f"Error reading {shopmy_file}: {e}")
        sys.exit(1)
    
    return domains_with_clicks

def filter_domains_without_clicks(input_file, domains_with_clicks, output_file, category):
    """
    Filter domains from input file that don't have clicks in shopmy.csv
    
    Args:
        input_file (str): Path to input CSV file (makeup or furniture)
        domains_with_clicks (set): Set of domains that have clicks
        output_file (str): Path to output CSV file
        category (str): Category name for logging (makeup/furniture)
        
    Returns:
        tuple: (total_domains, filtered_domains, domains_without_clicks)
    """
    try:
        df = pd.read_csv(input_file)
        print(f"\nProcessing {category} file: {input_file}")
        print(f"Loaded {len(df)} rows")
        
        # Normalize domains in the input file
        df['normalized_domain'] = df['domain'].apply(lambda x: x.lower().replace('www.', '') if pd.notna(x) else None)
        
        # Filter out domains that have clicks
        df_filtered = df[~df['normalized_domain'].isin(domains_with_clicks)]
        
        # Remove the temporary normalized_domain column
        df_filtered = df_filtered.drop('normalized_domain', axis=1)
        
        # Save filtered results
        df_filtered.to_csv(output_file, index=False)
        
        total_domains = len(df)
        filtered_domains = len(df_filtered)
        domains_without_clicks = total_domains - filtered_domains
        
        print(f"Total {category} domains: {total_domains}")
        print(f"Domains with 0 clicks: {filtered_domains}")
        print(f"Domains filtered out (have clicks): {domains_without_clicks}")
        print(f"Saved filtered results to: {output_file}")
        
        return total_domains, filtered_domains, domains_without_clicks
        
    except Exception as e:
        print(f"Error processing {input_file}: {e}")
        return 0, 0, 0

def print_sample_domains(domains_with_clicks, sample_size=10):
    """Print a sample of domains that have clicks for verification"""
    print(f"\nSample of domains with clicks (showing {min(sample_size, len(domains_with_clicks))} out of {len(domains_with_clicks)}):")
    for i, domain in enumerate(sorted(list(domains_with_clicks))[:sample_size]):
        print(f"  {i+1}. {domain}")
    if len(domains_with_clicks) > sample_size:
        print(f"  ... and {len(domains_with_clicks) - sample_size} more")

def main():
    """Main function to orchestrate the filtering process"""
    
    # File paths
    shopmy_file = "shopmy.csv"
    makeup_file = "AllShopMyMerchants7-1-25 - makeup.csv"
    furniture_file = "AllShopMyMerchants7-1-25 - furniture.csv"
    
    # Output file paths
    makeup_output = "makeup_domains_no_clicks.csv"
    furniture_output = "furniture_domains_no_clicks.csv"
    
    print("=" * 60)
    print("DOMAIN FILTERING SCRIPT")
    print("=" * 60)
    
    # Check if input files exist
    for file_path in [shopmy_file, makeup_file, furniture_file]:
        if not Path(file_path).exists():
            print(f"Error: File '{file_path}' not found!")
            sys.exit(1)
    
    print("All input files found. Starting processing...")
    
    # Step 1: Get domains with clicks from shopmy.csv
    print("\nStep 1: Extracting domains with clicks from shopmy.csv")
    domains_with_clicks = get_domains_with_clicks(shopmy_file)
    
    # Print sample for verification
    print_sample_domains(domains_with_clicks)
    
    # Step 2: Filter makeup domains
    print("\nStep 2: Filtering makeup domains")
    makeup_total, makeup_filtered, makeup_removed = filter_domains_without_clicks(
        makeup_file, domains_with_clicks, makeup_output, "makeup"
    )
    
    # Step 3: Filter furniture domains
    print("\nStep 3: Filtering furniture domains")
    furniture_total, furniture_filtered, furniture_removed = filter_domains_without_clicks(
        furniture_file, domains_with_clicks, furniture_output, "furniture"
    )
    
    # Summary
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    print(f"Domains with clicks in shopmy.csv: {len(domains_with_clicks)}")
    print(f"\nMakeup domains:")
    print(f"  Total: {makeup_total}")
    print(f"  With 0 clicks (kept): {makeup_filtered}")
    print(f"  With >0 clicks (filtered out): {makeup_removed}")
    print(f"  Percentage filtered out: {(makeup_removed/makeup_total*100):.1f}%" if makeup_total > 0 else "  Percentage filtered out: 0%")
    
    print(f"\nFurniture domains:")
    print(f"  Total: {furniture_total}")
    print(f"  With 0 clicks (kept): {furniture_filtered}")
    print(f"  With >0 clicks (filtered out): {furniture_removed}")
    print(f"  Percentage filtered out: {(furniture_removed/furniture_total*100):.1f}%" if furniture_total > 0 else "  Percentage filtered out: 0%")
    
    print(f"\nOutput files created:")
    print(f"  {makeup_output}")
    print(f"  {furniture_output}")
    print("\nScript completed successfully!")

if __name__ == "__main__":
    main()
