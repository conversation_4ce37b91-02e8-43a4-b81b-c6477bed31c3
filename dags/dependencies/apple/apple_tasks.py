"""
Apple Analytics DAG Task Functions

This module contains all the task functions for the Apple Analytics DAG,
following the same patterns as the Strackr implementation.
"""

import os
import logging
import gzip
import time
from datetime import datetime, timedelta
from typing import Dict, Any, List
from supabase import create_client, Client
from google.cloud import secretmanager

from .apple_api_client import AppleAnalyticsClient
from .apple_models import transform_csv_data, AppleAnalyticsNormalized
from .apple_config import (
    TARGET_REPORTS,
    get_enabled_reports,
    REPORT_PROCESSING_CONFIG,
    SUPABASE_CONFIG,
)

logger = logging.getLogger(__name__)


def _get_credential(env_var: str, secret_name: str) -> str:
    """Get credential from environment variable or Secret Manager."""

    logger.info(f"🔍 _get_credential called with env_var='{env_var}', secret_name='{secret_name}'")

    logger.info(f"Getting credential for {secret_name}...")

    # Try environment variable first
    value = os.getenv(env_var)
    if value:
        logger.info(f"✅ Retrieved credential {secret_name} from environment variable")
        return value

    # Try Secret Manager
    try:
        logger.info(f"🔍 Attempting to retrieve credential {secret_name} from Secret Manager")
        start_time = time.time()

        client = secretmanager.SecretManagerServiceClient()
        project_id = "609155540540"
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"

        logger.info(f"Secret path: {secret_path}")
        response = client.access_secret_version(request={"name": secret_path})

        secret_time = time.time() - start_time
        logger.info(f"✅ Successfully retrieved credential {secret_name} from Secret Manager in {secret_time:.2f} seconds")

        return response.payload.data.decode("UTF-8")

    except Exception as e:
        logger.error(f"❌ Failed to get credential {secret_name} from Secret Manager: {e}")
        logger.error(f"Exception type: {type(e).__name__}")
        return ""


def fetch_apple_analytics_data(**context) -> Dict[str, Any]:
    """
    Fetch Apple Analytics data for all enabled reports.

    Returns:
        Dictionary with fetch results and downloaded report data
    """
    logger.info("🍎 Starting Apple Analytics data fetch")

    logger.info("📋 Credential retrieval process starting...")

    try:
        # Initialize Apple API client
        logger.info("🔑 Fetching Apple App ID credential...")
        app_id = _get_credential("APPLE_APP_ID", "apple-app-id")

        if not app_id:
            logger.error("❌ Apple App ID credential is empty or missing")
            raise ValueError("Apple App ID is required")
        
        logger.info(f"✅ Successfully retrieved Apple App ID: {app_id[:10]}...")
        logger.info("🔧 Initializing Apple Analytics client...")
        client = AppleAnalyticsClient(app_id=app_id)
        logger.info("✅ Apple Analytics client initialized successfully")


        # Get enabled reports to fetch
        enabled_reports = get_enabled_reports()
        logger.info(f"📊 Fetching {len(enabled_reports)} enabled reports")

        # Use existing ongoing report request
        report_request_id = REPORT_PROCESSING_CONFIG["existing_report_request_id"]
        logger.info(f"📋 Using existing report request: {report_request_id}")

        # Fetch all available reports
        all_reports = client._make_request(
            "GET", f"/analyticsReportRequests/{report_request_id}/reports"
        )
        available_reports = all_reports.json().get("data", [])

        logger.info(f"📊 Found {len(available_reports)} available reports")

        # Match enabled reports with available reports
        fetched_data = {}
        successful_fetches = 0
        failed_fetches = 0

        for report_config in enabled_reports:
            try:
                logger.info(f"🔍 Looking for report: {report_config.name}")

                # Find matching report
                matching_report = None
                for report in available_reports:
                    if report.get("attributes", {}).get("name") == report_config.name:
                        matching_report = report
                        break

                if not matching_report:
                    logger.warning(f"⚠️ Report not found: {report_config.name}")
                    failed_fetches += 1
                    continue

                report_id = matching_report["id"]
                logger.info(f"✅ Found report: {report_id}")

                # Get latest instance
                try:
                    instance_id = client.get_instances(report_id)
                    segment_url = client.get_segments(instance_id)

                    # Download report data
                    raw_data = client.download_segment(segment_url)

                    # Decompress if needed
                    if isinstance(raw_data, bytes):
                        try:
                            csv_content = gzip.decompress(raw_data).decode("utf-8")
                        except:
                            csv_content = raw_data.decode("utf-8")
                    else:
                        csv_content = raw_data

                    # Store the data
                    fetched_data[report_config.name] = {
                        "report_id": report_id,
                        "instance_id": instance_id,
                        "csv_content": csv_content,
                        "config": (
                            report_config.dict()
                            if hasattr(report_config, "dict")
                            else {
                                "name": report_config.name,
                                "category": report_config.category.value,
                                "priority": report_config.priority,
                            }
                        ),
                    }

                    successful_fetches += 1
                    logger.info(f"✅ Successfully fetched: {report_config.name}")

                except Exception as e:
                    logger.error(f"❌ Failed to fetch {report_config.name}: {e}")
                    failed_fetches += 1
                    continue

            except Exception as e:
                logger.error(f"❌ Error processing report {report_config.name}: {e}")
                failed_fetches += 1
                continue

        # Return results
        result = {
            "success": successful_fetches > 0,
            "total_enabled": len(enabled_reports),
            "successful_fetches": successful_fetches,
            "failed_fetches": failed_fetches,
            "fetched_data": fetched_data,
            "fetch_timestamp": datetime.utcnow().isoformat(),
        }

        logger.info(
            f"🎉 Fetch complete: {successful_fetches} successful, {failed_fetches} failed"
        )
        return result

    except Exception as e:
        logger.error(f"❌ Failed to fetch Apple Analytics data: {e}")
        return {
            "success": False,
            "error": str(e),
            "successful_fetches": 0,
            "failed_fetches": 0,
            "fetched_data": {},
        }


def transform_apple_analytics_data(**context) -> Dict[str, Any]:
    """
    Transform raw Apple Analytics data to normalized format.

    Returns:
        Dictionary with transformation results
    """
    logger.info("🔄 Starting Apple Analytics data transformation")

    try:
        # Get fetched data from previous task
        fetch_result = context["task_instance"].xcom_pull(
            task_ids="fetch_apple_analytics_data"
        )

        if not fetch_result["success"]:
            raise Exception(
                f"Fetch task failed: {fetch_result.get('error', 'Unknown error')}"
            )

        fetched_data = fetch_result["fetched_data"]
        logger.info(f"📊 Transforming {len(fetched_data)} reports")

        # Transform each report
        all_transformed_data = []
        transformation_stats = {}

        for report_name, report_data in fetched_data.items():
            try:
                logger.info(f"🔄 Transforming report: {report_name}")

                # Transform CSV data to normalized format
                csv_content = report_data["csv_content"]
                transformed_records = transform_csv_data(csv_content, report_name)

                # Add report metadata to each record
                for record in transformed_records:
                    record.data_source = (
                        f"apple_analytics_{report_name.lower().replace(' ', '_')}"
                    )

                all_transformed_data.extend(transformed_records)

                transformation_stats[report_name] = {
                    "total_records": len(transformed_records),
                    "report_id": report_data["report_id"],
                    "instance_id": report_data["instance_id"],
                }

                logger.info(
                    f"✅ Transformed {len(transformed_records)} records from {report_name}"
                )

            except Exception as e:
                logger.error(f"❌ Failed to transform {report_name}: {e}")
                transformation_stats[report_name] = {
                    "total_records": 0,
                    "error": str(e),
                }
                continue

        # Convert to dictionaries for serialization
        transformed_dicts = []
        for record in all_transformed_data:
            try:
                transformed_dicts.append(record.to_dict())
            except Exception as e:
                logger.error(f"❌ Failed to serialize record: {e}")
                continue

        # Return results
        result = {
            "success": len(transformed_dicts) > 0,
            "total_reports": len(fetched_data),
            "successful_transforms": len(
                [s for s in transformation_stats.values() if "error" not in s]
            ),
            "failed_transforms": len(
                [s for s in transformation_stats.values() if "error" in s]
            ),
            "total_records": len(transformed_dicts),
            "transformation_stats": transformation_stats,
            "transformed_data": transformed_dicts,
            "transform_timestamp": datetime.utcnow().isoformat(),
        }

        logger.info(
            f"🎉 Transformation complete: {len(transformed_dicts)} total records"
        )
        return result

    except Exception as e:
        logger.error(f"❌ Failed to transform Apple Analytics data: {e}")
        return {
            "success": False,
            "error": str(e),
            "total_records": 0,
            "transformed_data": [],
        }


def upload_to_supabase(**context) -> Dict[str, Any]:
    """
    Upload transformed Apple Analytics data to Supabase.

    Returns:
        Dictionary with upload results
    """
    logger.info("⬆️ Starting Supabase upload")

    try:
        # Get transformed data from previous task
        transform_result = context["task_instance"].xcom_pull(
            task_ids="transform_apple_analytics_data"
        )

        if not transform_result["success"]:
            raise Exception(
                f"Transform task failed: {transform_result.get('error', 'Unknown error')}"
            )

        transformed_data = transform_result["transformed_data"]
        if not transformed_data:
            logger.info("ℹ️ No data to upload")
            return {
                "success": True,
                "uploaded_count": 0,
                "message": "No data to upload",
            }

        logger.info(f"📤 Uploading {len(transformed_data)} records to Supabase")

        # Initialize Supabase client

        logger.info("🔑 Fetching Supabase credentials...")
        supabase_url = _get_credential("SUPABASE_URL", "SUPABASE_URL")
        supabase_key = _get_credential("SUPABASE_SERVICE_ROLE_KEY", "SUPABASE_SERVICE_ROLE_KEY")


        if not supabase_url or not supabase_key:
            logger.error("❌ Supabase credentials are missing")
            logger.error(f"   • Supabase URL present: {bool(supabase_url)}")
            logger.error(f"   • Supabase Key present: {bool(supabase_key)}")
            raise ValueError("Supabase credentials are required")


        logger.info(f"✅ Supabase URL retrieved: {supabase_url[:50]}...")
        logger.info(f"✅ Supabase Key retrieved: {'*' * len(supabase_key) if supabase_key else 'None'}")
        logger.info("🔧 Creating Supabase client...")
        supabase: Client = create_client(supabase_url, supabase_key)
        logger.info("✅ Supabase client created successfully")


        # Upload in batches
        batch_size = SUPABASE_CONFIG["batch_size"]
        table_name = SUPABASE_CONFIG["table_name"]
        conflict_column = SUPABASE_CONFIG["upsert_conflict_column"]

        total_uploaded = 0
        errors = []

        for i in range(0, len(transformed_data), batch_size):
            batch = transformed_data[i : i + batch_size]
            batch_num = i // batch_size + 1

            # Deduplicate within batch to avoid conflict errors
            seen_ids = set()
            deduplicated_batch = []
            for record in batch:
                unique_id = record.get("unique_id")
                if unique_id not in seen_ids:
                    seen_ids.add(unique_id)
                    deduplicated_batch.append(record)

            if len(deduplicated_batch) != len(batch):
                logger.warning(
                    f"⚠️ Batch {batch_num}: Removed {len(batch) - len(deduplicated_batch)} duplicate records within batch"
                )

            try:
                logger.info(
                    f"📦 Uploading batch {batch_num}: {len(deduplicated_batch)} records"
                )

                # Use upsert to handle duplicates
                result = (
                    supabase.table(table_name)
                    .upsert(deduplicated_batch, on_conflict=conflict_column)
                    .execute()
                )

                batch_uploaded = (
                    len(result.data) if result.data else len(deduplicated_batch)
                )
                total_uploaded += batch_uploaded

                logger.info(f"✅ Batch {batch_num} uploaded: {batch_uploaded} records")

            except Exception as e:
                error_msg = f"Batch {batch_num} failed: {str(e)}"
                logger.error(f"❌ {error_msg}")
                errors.append(error_msg)

                # Continue with next batch
                continue

        # Return results
        result = {
            "success": len(errors) == 0,
            "total_records": len(transformed_data),
            "uploaded_count": total_uploaded,
            "error_count": len(errors),
            "errors": errors,
            "upload_timestamp": datetime.utcnow().isoformat(),
            "transformation_stats": transform_result.get("transformation_stats", {}),
        }

        if result["success"]:
            logger.info(
                f"🎉 Upload complete: {total_uploaded}/{len(transformed_data)} records uploaded"
            )
        else:
            logger.error(
                f"❌ Upload completed with errors: {len(errors)} batches failed"
            )

        return result

    except Exception as e:
        logger.error(f"❌ Failed to upload to Supabase: {e}")
        return {
            "success": False,
            "error": str(e),
            "uploaded_count": 0,
            "total_records": 0,
        }


def validate_apple_credentials(**context) -> Dict[str, Any]:
    """
    Validate Apple API credentials and connectivity.

    Returns:
        Dictionary with validation results
    """
    logger.info("🔐 Validating Apple API credentials")

    try:

        # Test credential retrieval
        logger.info("🔑 Testing all Apple API credentials...")

        credentials = {
            "app_id": _get_credential("apple-app-id"),
            "key_id": _get_credential("apple-key-id"),
            "issuer_id": _get_credential("apple-issuer-id"),
            "private_key_path": _get_credential("apple-private-key-path"),
        }

        
        logger.info("📋 Credential validation summary:")
        logger.info(f"   • App ID present: {bool(credentials['app_id'])} - {credentials['app_id'][:10] if credentials['app_id'] else 'Missing'}...")
        logger.info(f"   • Key ID present: {bool(credentials['key_id'])} - {credentials['key_id'][:10] if credentials['key_id'] else 'Missing'}...")
        logger.info(f"   • Issuer ID present: {bool(credentials['issuer_id'])} - {credentials['issuer_id'][:10] if credentials['issuer_id'] else 'Missing'}...")
        logger.info(f"   • Private Key present: {bool(credentials['private_key_path'])} - {len(credentials['private_key_path']) if credentials['private_key_path'] else 0} characters")


        # Check for missing credentials
        missing_creds = [key for key, value in credentials.items() if not value]
        if missing_creds:
            raise ValueError(f"Missing credentials: {', '.join(missing_creds)}")

        # Test API connectivity
        client = AppleAnalyticsClient(app_id=credentials["app_id"])

        # Test JWT token generation
        token = client.jwt_service.generate_jwt_token()
        if not token:
            raise Exception("Failed to generate JWT token")

        # Test API call
        report_request_id = REPORT_PROCESSING_CONFIG["existing_report_request_id"]
        reports = client._make_request(
            "GET", f"/analyticsReportRequests/{report_request_id}/reports"
        )

        available_reports = reports.json().get("data", [])
        logger.info(
            f"✅ API connectivity verified: {len(available_reports)} reports available"
        )

        return {
            "success": True,
            "available_reports": len(available_reports),
            "credentials_validated": True,
            "validation_timestamp": datetime.utcnow().isoformat(),
        }

    except Exception as e:
        logger.error(f"❌ Credential validation failed: {e}")
        return {
            "success": False,
            "error": str(e),
            "credentials_validated": False,
        }
