#!/usr/bin/env python3
"""
Script to verify which domains were filtered out and check matching logic
"""

import pandas as pd
from urllib.parse import urlparse

def extract_domain_from_url(url):
    """Extract and normalize domain from URL"""
    if not url or pd.isna(url):
        return None
    
    try:
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # Remove www. prefix for normalization
        if domain.startswith('www.'):
            domain = domain[4:]
        
        # Remove port numbers if present
        if ':' in domain:
            domain = domain.split(':')[0]
        
        return domain if domain else None
        
    except Exception as e:
        print(f"Error parsing URL '{url}': {e}")
        return None

def main():
    # Load shopmy.csv and extract domains with clicks
    shopmy_df = pd.read_csv("shopmy.csv")
    shopmy_df['Clicks'] = pd.to_numeric(shopmy_df['Clicks'], errors='coerce')
    shopmy_with_clicks = shopmy_df[shopmy_df['Clicks'] > 0]
    
    # Extract domains from URLs
    url_column = shopmy_df.columns[-1]  # Last column should be URL
    shopmy_domains = set()
    
    for url in shopmy_with_clicks[url_column]:
        domain = extract_domain_from_url(url)
        if domain:
            shopmy_domains.add(domain)
    
    print(f"Found {len(shopmy_domains)} unique domains with clicks in shopmy.csv")
    
    # Load makeup file
    makeup_df = pd.read_csv("AllShopMyMerchants7-1-25 - makeup.csv")
    makeup_original = set(makeup_df['domain'].str.lower().str.replace('www.', '', regex=False))
    
    # Load furniture file  
    furniture_df = pd.read_csv("AllShopMyMerchants7-1-25 - furniture.csv")
    furniture_original = set(furniture_df['domain'].str.lower().str.replace('www.', '', regex=False))
    
    # Find intersections
    makeup_matches = makeup_original.intersection(shopmy_domains)
    furniture_matches = furniture_original.intersection(shopmy_domains)
    
    print(f"\nMakeup domains that match shopmy domains (should be filtered out):")
    for domain in sorted(makeup_matches):
        print(f"  - {domain}")
    
    print(f"\nFurniture domains that match shopmy domains (should be filtered out):")
    for domain in sorted(furniture_matches):
        print(f"  - {domain}")
    
    # Check some specific examples
    test_domains = ['31philliplim.com', 'asos.com', 'nordstrom.com', 'wayfair.com']
    print(f"\nChecking specific domains:")
    for domain in test_domains:
        in_shopmy = domain in shopmy_domains
        in_makeup = domain in makeup_original
        in_furniture = domain in furniture_original
        print(f"  {domain}:")
        print(f"    In shopmy (with clicks): {in_shopmy}")
        print(f"    In makeup file: {in_makeup}")
        print(f"    In furniture file: {in_furniture}")
    
    # Show some sample shopmy domains for verification
    print(f"\nSample shopmy domains (first 20):")
    for i, domain in enumerate(sorted(list(shopmy_domains))[:20]):
        print(f"  {i+1}. {domain}")

if __name__ == "__main__":
    main()
